require 'rails_helper'

RSpec.describe Brokerage do
  describe 'searchable' do
    describe '::es_import_query' do
      shared_examples 'eager-loaded association' do |name|
        let!(:company) { create :company, :broker, :with_city, :with_authority }

        it 'eager-loads association' do
          expect(described_class.es_import_query.first).to(satisfy { |c| c.association(name).loaded? })
        end
      end

      it_behaves_like 'eager-loaded association', :operating_authorities
      it_behaves_like 'eager-loaded association', :city
      it_behaves_like 'eager-loaded association', :freights
      it_behaves_like 'eager-loaded association', :authhists
    end

    describe '::index_name' do
      it 'returns elasticsearch index name' do
        expect(described_class.index_name).to eq 'brokerages_test'
      end
    end

    describe '#as_indexed_json' do
      let(:brokerage) { brokerage_profile.brokerage }
      let!(:brokerage_profile) { create :brokerage_profile, company: }
      let(:company) { create :company, **attributes }

      let(:attributes) { {} }

      it 'returns document hash' do
        expect(brokerage.as_indexed_json({})).to match hash_including('dot_number' => brokerage.dot_number)
      end

      context 'when authority exists' do
        let(:broker) { Date.new(2016, 4, 19) }
        let(:operating_authority) { create :operating_authority, company:, broker_authority: 'active' }

        before do
          Authhist.create(docket_number: operating_authority.docket_number, broker:)
        end

        it 'adds authority_date' do
          expect(brokerage.as_indexed_json({})).to match hash_including(authority_date: broker)
        end
      end

      context 'when preferred lanes are present' do
        let!(:preferred_lane) { create :brokerage_profile_preferred_lane, brokerage_profile: }

        it 'adds preferred lanes' do
          expect(brokerage.as_indexed_json({})).to(
            match(
              hash_including(
                preferred_lanes: [
                  {
                    pickup_city_id: [preferred_lane.pickup_city_id, preferred_lane.pickup_city.full_slug],
                    pickup_state_id: preferred_lane.pickup_state_id,
                    pickup_region_id: preferred_lane.pickup_region_id,
                    pickup_location: preferred_lane.pickup_city.location,
                    dropoff_city_id: [preferred_lane.dropoff_city_id, preferred_lane.dropoff_city.full_slug],
                    dropoff_state_id: preferred_lane.dropoff_state_id,
                    dropoff_region_id: preferred_lane.dropoff_region_id,
                    dropoff_location: preferred_lane.dropoff_city.location
                  }
                ]
              )
            )
          )
        end
      end

      context 'when review lanes are present' do
        let!(:review) { create :brokerage_review, :carrier, :approved, company: }
        let(:review_lane) { review.review_lanes.first }

        before do
          Brokerages::UpsertReviewLanes.call(company)
        end

        it 'adds review lanes' do
          expect(brokerage.as_indexed_json({})).to(
            match(
              hash_including(
                review_lanes: [
                  {
                    pickup_city_id: [review_lane.pickup_city_id, review_lane.pickup_city.full_slug],
                    pickup_state_id: review_lane.pickup_state_id,
                    pickup_region_id: review_lane.pickup_region_id,
                    pickup_location: review_lane.pickup_city.location,
                    dropoff_city_id: [review_lane.dropoff_city_id, review_lane.dropoff_city.full_slug],
                    dropoff_state_id: review_lane.dropoff_state_id,
                    dropoff_region_id: review_lane.dropoff_region_id,
                    dropoff_location: review_lane.dropoff_city.location
                  }
                ]
              )
            )
          )
        end
      end
    end
  end

  describe '#location' do
    let(:company) { create :company, :broker }
    let(:brokerage) { company.as_entity(:broker) }

    it 'returns location' do
      expect(brokerage.location).to be_a Brokerages::Location
    end
  end
end
