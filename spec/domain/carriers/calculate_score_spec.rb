require 'rails_helper'

RSpec.describe Carriers::CalculateScore do
  subject(:calculator) { described_class.new(carrier:, average_review_count:) }

  let(:company) { create :company }
  let(:carrier) { company.as_entity(:carrier) }
  let(:average_review_count) { 1.62 }

  describe '#call' do
    context 'when carrier has no reviews' do
      it 'returns nil' do
        expect(calculator.call).to be_nil
      end
    end

    context 'when carrier reviews do not qualify' do
      before do
        create :review, :approved, company:, related_to_carrier: true
        create :review, :rejected, company:, related_to_carrier: false
      end

      it 'returns nil' do
        expect(calculator.call).to be_nil
      end
    end

    context 'with qualifying reviews' do
      before do
        create :review, :approved, company:, timeliness: 9, cleanliness: 8, communication: 8,
                                   is_consider_next_time: true
        create :review, :approved, company:, timeliness: 7, cleanliness: 9, communication: 10,
                                   is_consider_next_time: true
        create :review, :approved, company:, timeliness: 4, cleanliness: 2, communication: 1,
                                   is_consider_next_time: false
      end

      it 'calculates score' do
        expect(calculator.call).to be_within(0.01).of(63.34)
      end
    end
  end
end
