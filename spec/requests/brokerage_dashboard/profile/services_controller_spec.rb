require 'rails_helper'
require_relative '../shared_examples'

RSpec.describe BrokerageDashboard::Profile::ServicesController do
  let(:brokerage_profile) { create :brokerage_profile }
  let(:brokerage) { brokerage_profile.brokerage }
  let(:brokerage_profile_user) { create :brokerage_profile_user, :verified, brokerage_profile: }
  let(:user) { brokerage_profile_user.user }

  describe 'GET show' do
    let(:url) { brokerage_dashboard_profile_services_url(brokerage) }

    it_behaves_like 'a brokerage dashboard controller action', :get

    context 'when brokerage user is a verified profile user' do
      let(:url) { brokerage_dashboard_profile_services_url(brokerage, as: user.to_param) }

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'PATCH update' do
    let(:url) { brokerage_dashboard_profile_services_url(brokerage) }

    it_behaves_like 'a brokerage dashboard controller action', :patch

    context 'when brokerage user is a verified profile user' do
      let(:url) { brokerage_dashboard_profile_services_url(brokerage, as: user.to_param) }

      it 'updates attributes' do
        patch url, params: { brokerage_profile: { truck_types: [truck_types(:van).id] } }
        expect(brokerage_profile.reload).to have_attributes truck_types: [truck_types(:van).id]
      end

      context 'when update fails' do
        before do
          allow_any_instance_of(BrokerageProfile).to receive(:update).and_return(false)
        end

        it 'responds with unprocessable status' do
          patch url, params: { brokerage_profile: { truck_types: [truck_types(:van).id] } }
          expect(response).to have_http_status :unprocessable_entity
        end
      end
    end
  end
end
