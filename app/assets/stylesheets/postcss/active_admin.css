@import 'tailwindcss3/base';
@import 'tailwindcss3/components';
@import 'tailwindcss3/utilities';
@import '../tom-select.css';

@layer components {
  .status-tag {
    &.verified,
    &[data-status='success'],
    &[data-status='approved'],
    &[data-status='verified'] {
      @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300;
    }
  }

  .status-tag {
    &.rejected,
    &[data-status='failure'],
    &[data-status='rejected'] {
      @apply bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300;
    }
  }
}
