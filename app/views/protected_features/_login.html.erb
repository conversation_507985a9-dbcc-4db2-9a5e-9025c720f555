<%= render ModalComponent.new(title: 'Login Required', class: 'w-96') do %>
  <p>
    In order to <strong><%= t("protected_features.login.#{feature}") %></strong>, you must be logged in.
  </p>

  <div class="flex justify-center">
    <%= link_to 'Sign in', sign_in_url(return_to: request.referer),
                data: { turbo_frame: '_top' }, class: 'btn default primary mx-auto x-link-sign-in my-4' %>
  </div>
<% end %>
