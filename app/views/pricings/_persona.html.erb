<% categories = { marketing: %i(carrier brokerage), prospecting: %i(broker dispatcher) } %>

<% content_for :body_content do %>
  <div class="contained-width relative">
    <div class="mx-auto px-6 md:px-0">
      <h2 class="py-10 text-2xl md:text-3xl lg:text-4xl text-center font-semibold text-black">
        Choose the plan that's right for you
      </h2>

      <div class="flex flex-col gap-2 mb-8 md:hidden">
        <label for="persona-pricing" class="font-semibold text-black">Category</label>
        <select data-controller="select-scroll-to" name="persona" id="persona-pricing"
                class="input w-full">
          <% { marketing: :carrier, prospecting: :broker }.each do |category, type| %>
            <option value="<%= url_for([type, :pricing]) %>" <%= category == model.category ? 'selected' : nil %>>
              <%= category.to_s.titleize %>
            </option>
          <% end %>
        </select>
      </div>

      <ul class="hidden md:flex justify-center -mb-px">
        <% categories.each do |category, types| %>
          <% classes = category == model.category ? 'bg-gray-100 border-gray-300 text-sm font-semibold text-black' : 'border-gray-200' %>
          <li>
            <%= link_to category.to_s.titleize, url_for([types.first, :pricing]),
                        class: "flex items-center px-6 border border-b-0 rounded-t-sm h-12 #{classes} #{model.border_class(category)}" %>
          </li>
        <% end %>
      </ul>
    </div>
  </div>

  <div class="px-6 md:px-0 bg-gray-100 border-t border-gray-300 pb-12">
    <div class="contained-width relative mt-12 space-y-12">
      <ul class="flex justify-center">
        <% categories[model.category].zip(['rounded-l-sm', '-ml-px rounded-r-sm']).each do |type, selector| %>
          <% css = model.type == type ? 'border-primary bg-primary-50 z-10 text-black font-semibold' : 'border-gray-300 bg-white' %>
          <li class="flex">
            <%= link_to t("pricing.#{type}.label"), url_for([type, :pricing]),
                        class: "flex-auto px-6 py-2 text-center border cursor-pointer #{css} #{selector}" %>
          </li>
        <% end %>
      </ul>

      <%= yield %>

      <% if content_for? :features %>
        <div class="flex justify-center mt-12">
          <a data-controller="scroll-to" href="#features" class="btn hollow primary">Compare Features</a>
        </div>
      <% end %>

      <% if content_for? :addons %>
        <div class="flex justify-center mt-12">
          <a data-controller="scroll-to" href="#addons" class="btn hollow primary">View Add-ons</a>
        </div>
      <% end %>
    </div>

    <% if content_for? :features %>
      <div id="features" class="contained-width mt-12">
        <%= yield :features %>
      </div>
    <% end %>

    <% if content_for? :addons %>
      <div id="addons" class="contained-width mt-12">
        <div class="text-2xl font-semibold text-black mb-12">Add-ons</div>

        <%= yield :addons %>
      </div>
    <% end %>
  </div>
<% end %>
