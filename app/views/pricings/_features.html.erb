<div class="mx-auto max-w-2xl lg:hidden">
  <% plans.each do |plan| %>
    <section class="border-b border-gray-300 mb-16 pb-12 last:border-b-0 last:pb-0">
      <div class="mb-8">
        <h2 class="text-xl font-semibold leading-6 text-black"><%= plan.label %></h2>
      </div>

      <% features.each do |section| %>
        <table class="w-full">
          <caption class="border-t border-gray-200 bg-gray-200 py-3 px-4 text-left text-sm font-semibold text-gray-900">
            <%= section['label'] %>
          </caption>
          <thead>
          <tr>
            <th class="sr-only" scope="col">Feature</th>
            <th class="sr-only" scope="col">Included</th>
          </tr>
          </thead>
          <tbody class="divide-y divide-gray-200">
          <% section['items'].each do |item| %>
            <tr>
              <th class="py-5 px-4 text-left text-sm font-normal w-full" scope="row">
                <%= raw(item['label']) %>
              </th>
              <td class="py-5 pr-4">
                <% if item[plan.name.to_s] %>
                  <%= svg_tag('circle-check-regular', class: 'w-5 h-5 fill-green-600') %>
                  <span class="sr-only">Yes</span>
                <% else %>
                  <%= svg_tag('dash-regular', class: 'w-5 h-5 fill-gray-300') %>
                  <span class="sr-only">No</span>
                <% end %>
              </td>
            </tr>
          <% end %>
          </tbody>
        </table>
      <% end %>
    </section>
  <% end %>
</div>

<div class="hidden lg:block">
  <table class="h-px w-full table-fixed">
    <caption class="sr-only">
      Pricing plan comparison
    </caption>
    <thead>
    <tr>
      <th scope="col"></th>

      <% plans.each do |plan| %>
        <th class="w-1/4 px-6 pb-4 text-center text-lg font-semibold leading-6 text-black" scope="col">
          <%= plan.label %>
        </th>
      <% end %>
    </tr>
    </thead>

    <tbody class="divide-y divide-gray-200 border-t border-gray-200">

    <% features.each do |section| %>
      <tr>
        <th class="bg-gray-200 py-3 pl-6 text-left text-sm font-semibold text-black" colspan="4" scope="colgroup">
          <%= section['label'] %>
        </th>
      </tr>

      <% section['items'].each do |item| %>
        <tr>
          <th class="py-5 px-6 text-left text-sm font-normal" scope="row"><%= raw(item['label']) %></th>
          <% plans.each do |plan| %>
            <td class="py-5 px-6">
              <% if item[plan.name.to_s] %>
                <%= svg_tag('circle-check-regular', class: 'w-5 h-5 fill-green-600 mx-auto') %>
                <span class="sr-only">Included in <%= plan.label %></span>
              <% else %>
                <%= svg_tag('dash-regular', class: 'w-5 h-5 fill-gray-300 mx-auto') %>
                <span class="sr-only">Not included in <%= plan.label %></span>
              <% end %>
            </td>
          <% end %>
        </tr>
      <% end %>
    <% end %>
    </tbody>
  </table>
</div>
