<%= turbo_stream.update 'app-notifications' do %>
  <%= render NotificationComponent.new(type: :notice) do |c| %>
    <%= c.with_title { 'Saved successfully' } %>
  <% end %>
<% end %>

<%= turbo_stream.update 'modal' %>

<%= turbo_stream.replace dom_id(@availability, 'desktop') do %>
  <%= render 'carrier_dashboard/availabilities/desktop_availability', carrier: @carrier, availability: @availability %>
<% end %>

<%= turbo_stream.replace dom_id(@availability, 'mobile') do %>
  <%= render 'carrier_dashboard/availabilities/mobile_availability', carrier: @carrier, availability: @availability %>
<% end %>
