<div class="md:pb-[506px]"></div>

<footer class="md:absolute flex w-full bottom-0 md:h-[506px] bg-[#003351] flex-col items-center overflow-hidden
               x-footer">
  <div class="flex flex-col-reverse contained-width justify-center md:inline-grid md:grid-cols-3 gap-6 px-6 md:px-0 py-8
              text-white text-2xs">
    <% featured_loads = TruckingCompanies::Loads::Featured.instance.all %>
    <% city_slugs = ENV['POPULAR_CITY_SLUGS'].to_s.split(',').first(featured_loads.size) %>

    <div>
      <h6 class="text-lg mb-3">
        <strong class="text-white">Top Carriers</strong>
        <span class="opacity-80">– by shipment type</span>
      </h6>
      <ul class="space-y-1.5">
        <% featured_loads.each do |record| %>
          <li>
            <%= link_to load_trucking_companies_url(record.slug), class: 'hover:underline' do %>
              <strong class="text-white"><%= record.seo_name %></strong>
              <span class="opacity-75"><%= record.seo_type %></span>
            <% end %>
          </li>
        <% end %>
      </ul>
    </div>

    <div>
      <h6 class="text-lg mb-3">
        <strong class="text-white">Top Trucking Companies</strong>
        <span class="opacity-80">– by location</span>
      </h6>
      <ul class="space-y-1.5">
        <% city_slugs.each do |slug_with_name| %>
          <% slug, name = slug_with_name.split('::') %>
          <li>
            <%= link_to city_trucking_companies_url(*slug.split(':')) do %>
              <strong class="text-white"><%= name %></strong>
              <span class="opacity-75">Trucking Companies</span>
            <% end %>
          </li>
        <% end %>
      </ul>
    </div>
    <div class="md:border-l border-white md:pl-6">
      <h6 class="text-lg mb-3 font-semibold">Company and Policies</h6>
      <ul>
        <li class="py-0.5">
          <%= link_to 'Trucking Companies', trucking_companies_url,
                      class: 'x-link-trucking-companies hover:underline' %>
        </li>
        <li class="py-0.5">
          <%= link_to 'Pricing', pricing_url, class: 'x-link-pricing hover:underline' %>
        </li>
        <li class="py-0.5">
          <%= link_to 'CarrierSource Blog', 'https://company.carriersource.io/blog', class: 'hover:underline' %>
        </li>
        <li class="py-0.5">
          <%= link_to 'About CarrierSource', 'https://company.carriersource.io/about',
                      class: 'x-link-about hover:underline' %>
        </li>
      </ul>

      <%= render HorizontalRuleComponent.new %>

      <ul>
        <li class="py-0.5">
          <%= link_to 'Terms of Service', page_url('terms-of-service'), class: 'x-link-tos hover:underline' %>
        </li>
        <li class="py-0.5">
          <%= link_to 'Privacy Policy', page_url('privacy-policy'), class: 'x-link-privacy-policy hover:underline' %>
        </li>
        <li class="py-0.5">
          <%= link_to 'Cookie Policy', page_url('cookie-policy'), class: 'x-link-cookie-policy hover:underline' %>
        </li>
        <li class="py-0.5">
          <%= link_to 'Content and Data Usage', page_url('content-and-data-usage'),
                      class: 'x-link-content-data-usage hover:underline' %>
        </li>
      </ul>

      <%= render HorizontalRuleComponent.new %>

      <ul>
        <li class="py-0.5">
          <%= link_to 'Sign In', sign_in_url, class: 'x-link-sign-in hover:underline' %>
        </li>
        <li class="py-0.5">
          <%= link_to 'Sign Up', sign_up_url, class: 'x-link-sign-up hover:underline' %>
        </li>
      </ul>

      <div class="flex mt-2 space-x-0.5">
        <%= link_to 'https://www.linkedin.com/company/carriersource/', target: '_blank', rel: 'noopener',
                    class: 'hover:scale-105' do %>
          <%= svg_tag('linkedin', class: 'fill-white h-8 w-8', title: 'LinkedIn Logo') %>
        <% end %>
        <%= link_to 'https://www.facebook.com/thecarriersource', target: '_blank', rel: 'noopener',
                    class: 'hover:scale-105' do %>
          <%= svg_tag('square-facebook', class: 'fill-white h-8 w-8', title: 'Facebook Logo') %>
        <% end %>
        <%= link_to 'https://www.instagram.com/thecarriersource/', target: '_blank', rel: 'noopener',
                    class: 'hover:scale-105' do %>
          <%= svg_tag('square-instagram', class: 'fill-white h-8 w-8', title: 'Instagram Logo') %>
        <% end %>
        <%= link_to 'https://twitter.com/CarrierSource', target: '_blank', rel: 'noopener',
                    class: 'hover:scale-105' do %>
          <%= svg_tag('square-twitter', class: 'fill-white h-8 w-8', title: 'Twitter Logo') %>
        <% end %>
        <%= mail_to '<EMAIL>', target: '_blank', rel: 'noopener',
                    class: 'hover:scale-105' do %>
          <%= svg_tag('square-envelope-solid', class: 'fill-white h-8 w-8', title: 'Email') %>
        <% end %>
      </div>

    </div>
  </div>

  <div class="flex w-full h-[48px] justify-center items-center text-white text-2xs border-t border-gray-200/[0.5]">
    &copy;<%= Time.zone.today.year %> CarrierSource, Inc. All rights reserved
  </div>
</footer>
