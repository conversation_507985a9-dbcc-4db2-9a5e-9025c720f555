<% entity = local_assigns.fetch(:entity) %>
<% namespace = [entity.model_name.param_key, 'dashboard'].join('_') %>

<div class="flex">
  <div class="flex-auto">
    <h1>
      <strong class="text-2xl"><%= t("#{namespace}.navigation.pages.shipper_intent.targets") %></strong>
      <span class="block md:inline md:before:pr-1 md:before:content-['—'] font-light"><%= entity.name %></span>
    </h1>

    <p class="my-3">
      Build your ideal customer profiles (ICPs) and target the right shippers for your business. Define company type,
      size and signals for each target.
    </p>

    <div class="flex justify-end my-4">
      <%= link_to url_for([:new, entity, :dashboard, :analytics, :feed]), class: 'btn default primary' do %>
        <%= svg_tag 'plus-solid', class: 'h-4 w-4 mr-2 fill-white' %>
        <span>Add New Target</span>
      <% end %>
    </div>

    <ul class="flex flex-col gap-3">
      <% feeds.each do |feed| %>
        <li class="flex flex-row py-3 px-6 border border-gray-300 rounded-sm items-center">
          <strong class="flex-auto"><%= feed.name %></strong>

          <% if profile.access_to_feature?(:shipper_intent_integration) && feed.editable? %>
            <%= link_to url_for([entity, :dashboard, :analytics, :feed, :integrations, feed_id: feed]),
                        class: 'p-2 hover:bg-primary-100 border border-transparent hover:border-primary-400 rounded-sm group/integration',
                        data: { controller: 'tippy', tippy_content: 'Integrations', turbo_frame: 'modal' } do %>
              <%= svg_tag 'link-solid', class: 'w-4 h-4 fill-gray-600 group-hover/integration:fill-primary' %>
            <% end %>

            <div class="w-px bg-gray-300 mx-2 h-8"></div>
          <% end %>

          <% if feed.editable? %>
            <%= link_to url_for([:edit, entity, :dashboard, :analytics, :feed, id: feed]),
                        class: 'p-2 hover:bg-primary-100 border border-transparent hover:border-primary-400 rounded-sm group/edit',
                        data: { controller: 'tippy', tippy_content: 'Edit' } do %>
              <%= svg_tag 'pen-to-square-regular', class: 'w-4 h-4 fill-gray-600 group-hover/edit:fill-black' %>
            <% end %>
          <% else %>
            <span class="p-2 border border-transparent rounded-sm cursor-not-allowed" data-controller="tippy"
                  data-tippy-content="You cannot edit the default target">
              <%= svg_tag 'pen-to-square-regular', class: 'w-4 h-4 fill-gray-300' %>
            </span>
          <% end %>

          <%= link_to url_for([entity, :dashboard, :analytics, :feed, :shipper_intents, feed_id: feed]),
                      class: 'p-2 hover:bg-primary-100 border border-transparent hover:border-primary-400 rounded-sm group/view',
                      data: { controller: 'tippy', tippy_content: 'View Signals' } do %>
            <%= svg_tag 'eye-regular', class: 'w-4 h-4 fill-gray-600 group-hover/view:fill-black' %>
          <% end %>

          <%= link_to url_for([:duplicate, entity, :dashboard, :analytics, :feed, id: feed]),
                      class: 'p-2 hover:bg-primary-100 border border-transparent hover:border-primary-400 rounded-sm group/duplicate',
                      data: { controller: 'tippy', tippy_content: 'Clone', turbo_method: :post } do %>
            <%= svg_tag 'clone-solid', class: 'w-4 h-4 fill-gray-600 group-hover/duplicate:fill-black' %>
          <% end %>

          <% if feed.editable? %>
            <%= link_to url_for([entity, :dashboard, :analytics, :feed, id: feed]),
                        class: 'p-2 hover:bg-red-100 border border-transparent hover:border-red-400 rounded-sm group/delete',
                        data: { controller: 'tippy', tippy_content: 'Delete', turbo_method: :delete,
                                turbo_confirm: 'Are you sure you want to delete this?' } do %>
              <%= svg_tag 'trash-regular', class: 'w-4 h-4 fill-gray-600 group-hover/delete:fill-red-400' %>
            <% end %>
          <% else %>
            <div class="p-2 border border-transparent rounded-sm cursor-not-allowed" data-controller="tippy"
                 data-tippy-content="You cannot delete the default target">
              <%= svg_tag 'trash-regular', class: 'w-4 h-4 fill-gray-300' %>
            </div>
          <% end %>
        </li>
      <% end %>
    </ul>
  </div>
</div>
