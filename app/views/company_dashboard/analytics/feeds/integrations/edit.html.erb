<h1 class="mb-4">
  <strong class="text-2xl">Integration Settings</strong>
  <span class="hidden md:inline md:before:pr-1 md:before:content-['—'] font-light"><%= @profile.entity.name %></span>
</h1>

<%= svg_tag "integrations/#{@notification.notification_type}", class: 'h-12 max-h-12 max-w-16 mb-4' %>

<%= form_with model: @notification, builder: ApplicationFormBuilder,
              url: url_for([@profile.entity, :dashboard, :analytics, :feed, :integration, feed_id: @feed.id, id: @notification.id]) do |form| %>
  <div class="space-y-4">
    <fieldset class="error-container">
      <legend class="text-black font-semibold">Interval</legend>
      <ul>
        <% Analytics::CompanyEventFeedNotification::INTERVALS.each do |value| %>
          <li class="flex items-center mt-1.5">
            <%= form.radio_button_with_label :interval, value, required: true %>
          </li>
        <% end %>
      </ul>
    </fieldset>

    <%= form.fields_for :properties, form.object.properties do |prop_form| %>
      <% if @notification.notification_type == 'slack' %>
        <%= prop_form.fields_for :slack, prop_form.object.slack do |slack_form| %>
          <div class="flex flex-col gap-1">
            <%= slack_form.label :channel, 'Slack Channel', class: 'text-black font-semibold' %>
            <%= slack_form.select :channel, @data.retrieve(:channels), { include_blank: true },
                                  class: 'rounded-sm w-full md:w-1/2 lg:w-1/3',
                                  data: { controller: 'selectize' } %>
          </div>
        <% end %>
      <% elsif @notification.notification_type == 'teams' %>
        <%= prop_form.fields_for :teams, prop_form.object.teams do |teams_form| %>
          <div class="flex flex-col gap-1">
            <%= teams_form.label :channel_team_id, 'Teams Channel', class: 'text-black font-semibold' %>
            <%= teams_form.select :channel_team_id, @data.retrieve(:channels), { include_blank: true },
                                  class: 'rounded-sm w-full md:w-1/2 lg:w-1/3',
                                  data: { controller: 'selectize' } %>
          </div>
        <% end %>
      <% end %>
    <% end %>

    <div class="flex gap-2">
      <button type="submit" class="btn default primary">Save</button>
      <%= link_to 'Cancel', url_for([@profile.entity, :dashboard, :analytics, :feeds]), class: 'btn hollow gray' %>
    </div>
  </div>
<% end %>
