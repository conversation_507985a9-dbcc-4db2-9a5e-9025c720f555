<%= turbo_frame_tag 'modal' do %>
  <%= render ModalComponent.new(title: "Connect #{@feed.name} with:", class: 'w-5/6 md:w-1/2 xl:w-1/3') do %>
    <% if @notifications.present? %>
      <ul class="flex flex-col border border-gray-300 rounded-sm divide-y divide-gray-300">
        <% @notifications.each do |notification| %>
          <li class="flex items-center px-6 py-2 gap-2">
          <span class="flex-auto">
            <%= svg_tag "integrations/#{notification.notification_type}", class: 'h-12 max-h-12 max-w-16' %>
          </span>
            <% if notification.persisted? %>
              <%= link_to url_for([:edit, @profile.entity, :dashboard, :analytics, :feed, :integration, feed_id: @feed.id, id: notification.id]),
                          class: 'p-3 border border-transparent hover:border-gray-600 hover:bg-gray-100 rounded-sm',
                          data: { turbo_frame: '_top' } do %>
                <%= svg_tag 'gear-regular', class: 'w-4 h-4 fill-gray-600' %>
              <% end %>

              <%= form_with url: url_for([@profile.entity, :dashboard, :analytics, :feed, :integration, feed_id: @feed.id, id: notification.id]),
                            method: :delete, data: { turbo: false } do %>
                <button type="submit"
                        class="flex items-center gap-2 px-2 py-[9px] border border-primary hover:bg-primary-100 rounded-sm">
                  <%= svg_tag 'mobile_checkbox_selected', class: 'w-3 h-3' %>
                  <span class="text-primary">Connected</span>
                </button>
              <% end %>
            <% else %>
              <%= form_with model: notification,
                            url: url_for([@profile.entity, :dashboard, :analytics, :feed, :integrations, feed_id: @feed.id]),
                            data: { turbo: false } do |form| %>
                <%= form.hidden_field :notification_type %>
                <%= form.hidden_field :integration_id %>

                <button type="submit"
                        class="flex items-center gap-2 px-2 py-[9px] border border-primary hover:bg-primary-100 rounded-sm">
                  <%= svg_tag 'mobile_checkbox_default', class: 'w-3 h-3 fill-black' %>
                  <span class="text-primary">Connect</span>
                </button>
              <% end %>
            <% end %>
          </li>
        <% end %>
      </ul>
    <% end %>

    <div data-controller="clipboard" data-clipboard-success-content-value="Copied!" class="flex items-end space-x-2 my-4">
      <div class="flex-col flex-auto">
        <label for="feed-uuid" class="block after:content-[':'] font-semibold text-black">Target Identifier</label>
        <input id="feed-uuid" type="text" value="<%= @feed.uuid %>" data-clipboard-target="source"
               class="input w-full" readonly>
      </div>

      <button type="button" class="btn default primary" data-action="clipboard#copy">
        <%= svg_tag 'clipboard-regular', class: 'h-4 w-4 mr-2 fill-white' %>
        <span data-clipboard-target="button">Copy</span>
      </button>
    </div>
  <% end %>
<% end %>
