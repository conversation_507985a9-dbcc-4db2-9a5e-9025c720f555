ActiveAdmin.register BrokerageProfile do
  menu parent: 'Companies'

  actions :all, except: %i(new create destroy)

  permit_params :hide_street

  filter :company_dot_number, as: :numeric
  filter :company_name, as: :string

  action_item :edit_access_package, only: :show, if: -> { AccessPackage.exists?(resource:) } do
    link_to 'Edit Access Package',
            edit_admin_access_package_url(AccessPackage.where(resource:).order(active: :desc).first),
            class: 'action-item-button'
  end

  index pagination_total: false do
    selectable_column
    id_column
    column :company
    column :created_at
    actions
  end

  show do
    attributes_table do
      row :company
      row :bio

      row(:logo) do
        next unless resource.logo.attached?
        link_to 'Download Logo', resource.logo, target: '_blank', rel: 'noopener'
      end

      row :banner do
        next unless resource.banner.attached?
        link_to 'Download Banner', resource.banner, target: '_blank', rel: 'noopener'
      end

      %i(truck_types shipment_types specialized_services).each do |field|
        row(field) do
          Records[field].slice(*resource.public_send(field)).values.map(&:name).join(', ')
        end
      end

      row :created_at
      row :updated_at
    end
  end

  form do |f|
    f.inputs do
      f.input :hide_street
    end

    f.actions
  end

  controller do
    def scoped_collection
      end_of_association_chain.includes(:company)
    end
  end
end
