module CompanyDashboard
  module Analytics
    module Feeds
      module IntegrationsController
        extend ActiveSupport::Concern

        included do
          before_action :assign_feed

          def index
            @notifications =
              ::Analytics::Integration.active.where(
                company: @company, entity_type: @profile.entity.entity_type,
                provider: ::Analytics::Integration::ENABLED_PROVIDERS
              ).map do |integration|
                ::Analytics::CompanyEventFeedNotifications::FindOrInitialize.call(feed: @feed, integration:)
              end

            render template: 'company_dashboard/analytics/feeds/integrations/index'
          end

          def edit
            @notification = @feed.notifications.find(params[:id])
            @data = ::Analytics::CompanyEventFeedNotifications::IntegrationData.new(@notification.integration)
            render template: 'company_dashboard/analytics/feeds/integrations/edit'
          end

          def create
            @notification = @feed.notifications.build(notification_params)

            if @notification.save
              redirect_to url_for([:edit, @profile.entity, :dashboard, :analytics, :feed, :integration,
                                   { feed_id: @feed.id, id: @notification.id }]),
                          notice: 'Integration was successfully enabled.'
            else
              head :unprocessable_entity
            end
          end

          def update
            @notification = @feed.notifications.find(params[:id])

            if @notification.update(notification_params)
              redirect_to url_for([@profile.entity, :dashboard, :analytics, :feeds]),
                          notice: 'Integration was successfully updated.'
            else
              head :unprocessable_entity
            end
          end

          def destroy
            @notification = @feed.notifications.find(params[:id])
            @notification.destroy
            redirect_to url_for([@profile.entity, :dashboard, :analytics, :feeds]),
                        notice: 'Integration was successfully disabled.'
          end

          private

          def assign_feed
            @feed = ::Analytics::CompanyEventFeed.find(params[:feed_id])
          end

          def notification_params
            params.expect(
              analytics_company_event_feed_notification: [
                :notification_type, :integration_id, :interval, { properties: {} }
              ]
            )
          end
        end
      end
    end
  end
end
