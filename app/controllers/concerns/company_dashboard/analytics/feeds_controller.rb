module CompanyDashboard
  module Analytics
    module FeedsControll<PERSON>
      extend ActiveSupport::Concern

      included do
        before_action do
          @skip_profile_completion = true
          @navigation_page = Companies::EntityClassRegistry.lookup(entity: @profile.entity, type: :dashboard_navigation)
                               .page('shipper_intent.targets', @profile.entity, current_user, params)
        end

        def index
          @feeds =
            ::Analytics::CompanyEventFeed.where(company: @company, entity_type: @profile.entity.entity_type)
              .order(:created_at)
        end

        def new
          @feed = ::Analytics::CompanyEventFeed.new(company: @company, entity_type: @profile.entity.entity_type)
        end

        def edit
          @feed = ::Analytics::CompanyEventFeed.find(params[:id])
        end

        def create
          @feed = ::Analytics::CompanyEventFeed.new(
            feed_params.merge(company: @company, entity_type: @profile.entity.entity_type)
          )

          if @feed.save
            redirect_to url_for([@profile.entity, :dashboard, :analytics, :feeds]),
                        notice: 'Target was successfully created.'
          else
            render :new
          end
        end

        def update
          @feed = ::Analytics::CompanyEventFeed.find(params[:id])

          if @feed.update(feed_params)
            redirect_to url_for([@profile.entity, :dashboard, :analytics, :feeds]),
                        notice: 'Target was successfully updated.'
          else
            render :edit
          end
        end

        def destroy
          @feed = ::Analytics::CompanyEventFeed.find(params[:id])
          @feed.destroy!
          redirect_to url_for([@profile.entity, :dashboard, :analytics, :feeds]),
                      notice: 'Target was successfully removed.'
        end

        def duplicate
          feed = ::Analytics::CompanyEventFeed.find(params[:id]).dup
          feed.update!(name: "#{feed.name} (copy)", editable: true)
          redirect_to url_for([:edit, @profile.entity, :dashboard, :analytics, :feed, { id: feed.id }])
        end

        def export
          feed = ::Analytics::CompanyEventFeed.find(params[:id])
          ::Analytics::CompanyEventFeedExport.create(user: current_user, feed:, email: params[:email])

          respond_to do |format|
            format.html do
              redirect_to url_for([@profile.entity, :dashboard, :analytics, :feeds]), notice: 'Exporting target...'
            end

            format.turbo_stream
          end
        end

        private

        def feed_params
          params.expect(analytics_company_event_feed: [:name, { filters: {} }]).to_h.tap do |permitted|
            permitted[:filters] = Forms::AnalyticsEvent.new(permitted[:filters]).to_h
          end
        end
      end
    end
  end
end
