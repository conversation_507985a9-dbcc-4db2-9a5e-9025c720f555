module BrokerageDashboard
  class ProfilesController < ApplicationController
    def show
    end

    def update
      if @profile.update(profile_params)
        redirect_back_or_to brokerage_dashboard_profile_url(@brokerage), notice: 'Saved successfully'
      else
        render :show, status: :unprocessable_entity
      end
    end

    private

    def profile_params
      params.expect(brokerage_profile: %i(bio banner website_url purge_banner logo purge_logo))
    end
  end
end
