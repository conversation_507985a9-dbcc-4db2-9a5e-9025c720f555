module Analytics
  class IntegrationsController < ApplicationController
    before_action :assign_integration

    def oauth_redirect
      auth = Analytics::Integrations::Authentications.for(@integration)
      redirect_action = params[:code].present? ? auth.accept(params[:code]) : auth.error(params)
      redirect_to redirect_action.url, **redirect_action.options
    end

    private

    def assign_integration
      @integration = Analytics::Integration.find(params[:state])
    end
  end
end
