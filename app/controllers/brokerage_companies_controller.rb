class BrokerageCompaniesController < ApplicationController
  before_action :assign_country, except: :index
  before_action :redirect_to_slug, except: :index
  before_action :assign_state, except: %i(index country)
  before_action :assign_city, only: :city

  def index
  end

  def country
    @states =
      City.joins(:brokerage_cities).merge(City.where(country_code: @country.alpha2)).distinct.pluck(:state_code).to_set
        .then { |abbrs| @country.states.select { |state| abbrs.member?(state.abbr) } }
    perform_search(country_id: @country.slug)
  end

  def state
    @cities = City.joins(:brokerage_cities)
                .merge(City.where(country_code: @country.alpha2, state_code: @state.abbr))
                .order('population desc nulls last').limit(20)
    perform_search(state_id: @state.id)
  end

  def city
    perform_search(city_id: @city.id)
  end

  private

  def assign_country
    @country = Geo::Country.find(params[:country]) || raise(ActiveRecord::RecordNotFound)
  end

  def assign_state
    @state = @country.states.find_by(slug: params[:state]).presence || raise(ActiveRecord::RecordNotFound)
  end

  def assign_city
    @city = City.find_by!(country_code: @country.alpha2, state_code: @state.abbr, slug: params[:city])
  end

  def redirect_to_slug
    return if @country.slug == params[:country]
    redirect_to url_for(country: @country.slug, **params.permit(:controller, :action, :state, :city).to_h)
  end

  def perform_search(defaults = {})
    @search_form = Forms::Carrier.from_params(
      params, defaults: { sort: :cs_score, entity_type: :broker, **defaults }
    )
    @brokerages = Companies::SearchProviders::Standard.new(@search_form, params).records
    redirect_to Current.canonical_url if @brokerages.current_page > [@brokerages.total_pages, 1].max
  end
end
