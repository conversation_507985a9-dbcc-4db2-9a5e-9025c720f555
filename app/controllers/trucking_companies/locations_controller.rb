module TruckingCompanies
  class LocationsController < ApplicationController
    before_action :assign_country, except: :index
    before_action :redirect_to_slug, except: :index
    before_action :assign_state, except: %i(index country)
    before_action :assign_city, only: :city
    before_action :redirect_to_closest_city, only: :city

    def index
    end

    def country
      @states =
        City.joins(:carrier_cities).merge(City.where(country_code: @country.alpha2)).distinct.pluck(:state_code).to_set
          .then { |abbrs| @country.states.select { |state| abbrs.member?(state.abbr) } }
      perform_search(country_id: @country.slug)
    end

    def state
      @cities = City.joins(:carrier_cities)
                  .merge(City.where(country_code: @country.alpha2, state_code: @state.abbr))
                  .order('population desc nulls last').limit(20)
      perform_search(state_id: @state.id)
    end

    def city
      perform_search(city_id: @city.id)
      @expanded_provider = Companies::SearchProviders::Standard.new(@search_form.new(radius: 50), params)
    end

    private

    def assign_country
      @country = Geo::Country.find(params[:country]) || raise(ActiveRecord::RecordNotFound)
    end

    def assign_state
      @state = @country.states.find_by(slug: params[:state]).presence || raise(ActiveRecord::RecordNotFound)
    end

    def assign_city
      @city = City.find_by!(country_code: @country.alpha2, state_code: @state.abbr, slug: params[:city])
    end

    def redirect_to_slug
      return if @country.slug == params[:country]
      redirect_to url_for(country: @country.slug, **params.permit(:controller, :action, :state, :city).to_h)
    end

    def redirect_to_closest_city
      return if CarrierCity.exists?(city: @city)
      alternate_city = ::TruckingCompanies::AlternateCity.call(CarrierCity.all, @city)
      redirect_to city_trucking_companies_url(*alternate_city.path) if alternate_city.present?
    end

    def perform_search(defaults = {})
      @search_form = Forms::Carrier.from_params params, defaults: { sort: :cs_score, entity_type: :carrier, **defaults }
      @provider = Companies::SearchProviders::Standard.new(@search_form, params)
      customer_provider = Companies::SearchProviders::Standard.new(
        @search_form.new(radius: 50, upgraded: true), params
      )
      @companies = @provider.records
      @customers = customer_provider.records.sample(2).cycle(2).to_a

      redirect_to Current.canonical_url if @companies.current_page > [@companies.total_pages, 1].max
    end
  end
end
