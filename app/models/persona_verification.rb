# == Schema Information
#
# Table name: persona_verifications
#
#  id         :bigint           not null, primary key
#  company    :string           not null
#  email      :string
#  linkedin   :string
#  phone      :string           not null
#  status     :string           default("pending"), not null
#  title      :string           not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  persona_id :bigint           not null
#
# Indexes
#
#  index_persona_verifications_on_persona_id  (persona_id) UNIQUE
#
# Foreign Keys
#
#  fk_rails_95dd497475  (persona_id => personas.id) ON DELETE => cascade
#
class PersonaVerification < ApplicationRecord
  include AASM

  normalizes :company, :title, :phone, :email, :linkedin, with: Functions['normalizers.strip']

  belongs_to :persona

  validates :company, :title, :phone, presence: true

  delegate :user, to: :persona

  aasm column: :status do
    state :pending, initial: true
    state :approved
    state :rejected

    event :approve do
      transitions from: :pending, to: :approved
    end

    event :reject do
      transitions from: :pending, to: :rejected
    end

    event :reset do
      transitions from: %i(approved rejected), to: :pending
    end
  end
end
