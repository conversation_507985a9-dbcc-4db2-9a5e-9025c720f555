module CarrierLane
  extend ActiveSupport::Concern

  included do
    belongs_to :dropoff_city, class_name: 'City'
    belongs_to :pickup_city, class_name: 'City'
  end

  def pickup_state_id
    pickup_city.state.id
  end

  def pickup_region_id
    pickup_city.state.region.id
  end

  def dropoff_state_id
    dropoff_city.state.id
  end

  def dropoff_region_id
    dropoff_city.state.region.id
  end

  def pickup_options
    return [] if pickup_city.blank?

    [[pickup_city.label, pickup_city_id]]
  end

  def dropoff_options
    return [] if dropoff_city.blank?

    [[dropoff_city.label, dropoff_city_id]]
  end
end
