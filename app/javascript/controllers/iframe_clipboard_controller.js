import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
  static targets = ['button']
  static values = {
    iframeId: String,
    sourceId: String,
    successContent: String,
    successDuration: {
      type: Number,
      default: 2000
    }
  }

  connect() {
    if (this.hasButtonTarget) {
      this.originalContent = this.buttonTarget.innerHTML
    }
  }

  async copy(e) {
    e.preventDefault()

    try {
      const iframe = document.getElementById(this.iframeIdValue)
      const iframeWindow = iframe.contentWindow
      const iframeDocument = iframe.contentDocument || iframeWindow.document
      const sourceTarget = iframeDocument.getElementById(this.sourceIdValue)

      // Get the text content or HTML content to copy
      const textToCopy = sourceTarget.innerText || sourceTarget.textContent || sourceTarget.innerHTML

      // Use the modern clipboard API
      await navigator.clipboard.writeText(textToCopy)
      this.copied()
    } catch (error) {
      console.error('Failed to copy to clipboard:', error)
      // Fallback to the old method if modern API fails
      this.fallbackCopy()
    }
  }

  fallbackCopy() {
    try {
      const iframe = document.getElementById(this.iframeIdValue)
      const iframeWindow = iframe.contentWindow
      const iframeDocument = iframe.contentDocument || iframeWindow.document
      const sourceTarget = iframeDocument.getElementById(this.sourceIdValue)

      const selection = iframeWindow.getSelection()
      const range = iframeDocument.createRange()
      range.selectNodeContents(sourceTarget)
      selection.removeAllRanges()
      selection.addRange(range)

      const success = iframeDocument.execCommand('copy')
      selection.removeAllRanges()

      if (success) {
        this.copied()
      } else {
        console.error('Fallback copy method failed')
      }
    } catch (error) {
      console.error('Fallback copy method failed:', error)
    }
  }

  copied() {
    if (this.hasButtonTarget) {
      if (this.timeout) {
        clearTimeout(this.timeout)
      }

      this.buttonTarget.innerHTML = this.successContentValue

      this.timeout = setTimeout(() => {
        this.buttonTarget.innerHTML = this.originalContent
      }, this.successDurationValue)
    }
  }
}
