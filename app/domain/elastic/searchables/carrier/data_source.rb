module Elastic
  module Searchables
    module Carrier
      class DataSource
        LANE_MAPPER = proc do |lane|
          {
            pickup_city_id: [lane.pickup_city_id, lane.pickup_city.full_slug],
            pickup_state_id: lane.pickup_state_id,
            pickup_region_id: lane.pickup_region_id,
            pickup_location: lane.pickup_city.location,
            dropoff_city_id: [lane.dropoff_city_id, lane.dropoff_city.full_slug],
            dropoff_state_id: lane.dropoff_state_id,
            dropoff_region_id: lane.dropoff_region_id,
            dropoff_location: lane.dropoff_city.location
          }
        end

        attr_reader :carrier, :profile

        delegate :census, :reviews_aggregate, :street, :zip, to: :carrier
        delegate :carrier_mailing_street, :carrier_mailing_zip, to: :census, allow_nil: true
        delegate :cs_score, :claimed?, :upgraded?, to: :profile

        def initialize(carrier)
          @carrier = carrier
          @profile = @carrier.profile || Nullable.object(:carrier_profile)
        end

        # rubocop:disable Metrics/AbcSize
        def as_indexed_json(_options)
          carrier.as_json(
            only: %i(id dot_number slug safety_rating carrier_operation),
            methods: %i(name freight_ids truck_type_ids shipment_type_ids specialized_service_ids)
          ).merge(
            cs_score:, docket_number:, authorized: authorized?, authority_date:, city_id:, state_id:, country_id:,
            claimed: claimed?, fleet_size:, insurance:, location:, city: city.name, state: city.state_code, zip:,
            plain_docket_number:, preferred_lanes:, review_lanes:, operation_states:, physical_address:,
            mailing_address:, phone:, star_rating:, upgraded: upgraded?, authorities:
          )
        end
        # rubocop:enable Metrics/AbcSize

        private

        def authorities
          %w(common contract broker).select do |type|
            carrier.operating_authorities.any? { |oa| oa.public_send("#{type}_authority").casecmp?('active') }
          end
        end

        def carrier_location
          @carrier_location ||= carrier.location
        end

        def city
          carrier.city || Nullable.object(:city)
        end

        def authorized?
          carrier.authorized?
        end

        def authority_date
          carrier.authhists.flat_map { |authhist| [authhist.common, authhist.contract] }.compact.min
        end

        def city_id
          carrier_location.cities.map(&:id).presence
        end

        def state_id
          carrier_location.states.map(&:id).presence
        end

        def country_id
          carrier_location.countries.map(&:slug).presence
        end

        def star_rating
          reviews_aggregate&.star_rating
        end

        def fleet_size
          carrier.power_units
        end

        def location
          carrier_location.cities.map(&:location).presence
        end

        def insurance
          return if carrier.insurances.blank?

          carrier.insurances.max_by(&:policy_limit).policy_limit
        end

        def docket_number
          carrier.operating_authorities.pluck(:docket_number)
        end

        def operation_states
          profile.operation_states.pluck(:state_id)
        end

        def plain_docket_number
          carrier.operating_authorities.map { |oa| oa.docket_number[2..] }
        end

        def preferred_lanes
          profile.preferred_lanes.map(&LANE_MAPPER)
        end

        def review_lanes
          carrier.review_lanes.map(&LANE_MAPPER)
        end

        def physical_address
          [street, zip].compact_blank.join(' ')
        end

        def mailing_address
          [carrier_mailing_street, carrier_mailing_zip].compact_blank.join(' ')
        end

        def phone
          Functions::FormatPhone.call(phone: carrier.phone, country: city.country_code)
        end
      end
    end
  end
end
