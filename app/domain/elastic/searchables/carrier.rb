module Elastic
  module Searchables
    module Carrier
      extend ActiveSupport::Concern
      include Elastic::Searchable

      included do
        mappings dynamic: false do
          indexes :id, type: :keyword
          indexes :authorized, type: :boolean
          indexes :authority_date, type: :date, format: :date
          indexes :authorities, type: :keyword
          indexes :carrier_operation, type: :keyword
          indexes :city_id, type: :keyword
          indexes :state_id, type: :keyword
          indexes :country_id, type: :keyword
          indexes :claimed, type: :boolean
          indexes :cs_score, type: :float
          indexes :star_rating, type: :float
          indexes :upgraded, type: :boolean

          indexes :docket_number, type: :text, index_prefixes: {} do
            indexes :raw, type: :keyword
          end

          indexes :dot_number, type: :text, index_prefixes: {} do
            indexes :raw, type: :keyword
          end

          indexes :fleet_size, type: :integer
          indexes :freight_ids, type: :keyword
          indexes :insurance, type: :integer
          indexes :location, type: :geo_point

          indexes :name, type: :text do
            indexes :raw, type: :keyword
            indexes :suggest, type: :search_as_you_type
          end

          indexes :operation_states, type: :keyword

          indexes :plain_docket_number, type: :text, index_prefixes: {} do
            indexes :raw, type: :keyword
          end

          indexes :preferred_lanes, type: :nested do
            indexes :pickup_city_id, type: :keyword
            indexes :pickup_state_id, type: :keyword
            indexes :pickup_region_id, type: :keyword
            indexes :pickup_location, type: :geo_point
            indexes :dropoff_city_id, type: :keyword
            indexes :dropoff_state_id, type: :keyword
            indexes :dropoff_region_id, type: :keyword
            indexes :dropoff_location, type: :geo_point
          end

          indexes :review_lanes, type: :nested do
            indexes :pickup_city_id, type: :keyword
            indexes :pickup_state_id, type: :keyword
            indexes :pickup_region_id, type: :keyword
            indexes :pickup_location, type: :geo_point
            indexes :dropoff_city_id, type: :keyword
            indexes :dropoff_state_id, type: :keyword
            indexes :dropoff_region_id, type: :keyword
            indexes :dropoff_location, type: :geo_point
          end

          indexes :safety_rating, type: :keyword, null_value: 'none'
          indexes :shipment_type_ids, type: :keyword
          indexes :specialized_service_ids, type: :keyword
          indexes :truck_type_ids, type: :keyword

          indexes :physical_address, type: :text do
            indexes :raw, type: :keyword
          end

          indexes :mailing_address, type: :text do
            indexes :raw, type: :keyword
          end

          indexes :phone, type: :keyword
        end

        def as_indexed_json(options)
          DataSource.new(self).as_indexed_json(options)
        end
      end

      class_methods do
        def es_import_query
          indexable.carrier.preload(
            :city, :freights, :insurances, :truck_types, :shipment_types, :specialized_services,
            :operating_authorities, :authhists, :census, :reviews_aggregate,
            review_lanes: %i(pickup_city dropoff_city),
            profile: [:access_package, :operation_states, :users,
                      { preferred_lanes: %i(pickup_city dropoff_city), terminals: :city }]
          )
        end
      end
    end
  end
end
