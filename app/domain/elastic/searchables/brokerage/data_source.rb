module Elastic
  module Searchables
    module Brokerage
      class DataSource
        LANE_MAPPER = proc do |lane|
          {
            pickup_city_id: [lane.pickup_city_id, lane.pickup_city.full_slug],
            pickup_state_id: lane.pickup_state_id,
            pickup_region_id: lane.pickup_region_id,
            pickup_location: lane.pickup_city.location,
            dropoff_city_id: [lane.dropoff_city_id, lane.dropoff_city.full_slug],
            dropoff_state_id: lane.dropoff_state_id,
            dropoff_region_id: lane.dropoff_region_id,
            dropoff_location: lane.dropoff_city.location
          }
        end

        attr_reader :brokerage, :profile

        delegate :reviews_aggregate, to: :brokerage
        delegate :star_rating, to: :reviews_aggregate, allow_nil: true
        delegate :cs_score, :claimed?, :upgraded?, to: :profile

        def initialize(brokerage)
          @brokerage = brokerage
          @profile = brokerage.profile || Nullable.object(:brokerage_profile)
        end

        # rubocop:disable Metrics/AbcSize
        def as_indexed_json(_options)
          brokerage.as_json(
            only: %i(id dot_number slug),
            methods: %i(name freight_ids truck_type_ids shipment_type_ids specialized_service_ids)
          ).merge(
            docket_number:, authorized: authorized?, authority_date:, city_id: city.id, location: city.location,
            city: city.name, state: city.state_code, zip: brokerage.zip, state_id:, country_id:, plain_docket_number:,
            claimed: claimed?, cs_score:, preferred_lanes:, review_lanes:, star_rating:, upgraded: upgraded?
          )
        end
        # rubocop:enable Metrics/AbcSize

        private

        def city
          brokerage.city || Nullable.object(:city)
        end

        def state_id
          city.state.try(:id)
        end

        def country_id
          city.country.try(:id)
        end

        def authorized?
          operating_authorities.present?
        end

        def authority_date
          operating_authorities.map(&:authhist).map(&:broker).compact.min
        end

        def docket_number
          operating_authorities.pluck(:docket_number)
        end

        def plain_docket_number
          operating_authorities.map { |oa| oa.docket_number[2..] }
        end

        def operating_authorities
          @operating_authorities ||=
            brokerage.operating_authorities.select { |oa| oa.broker_authority.casecmp?('active') }
        end

        def preferred_lanes
          profile.preferred_lanes.map(&LANE_MAPPER)
        end

        def review_lanes
          brokerage.review_lanes.map(&LANE_MAPPER)
        end
      end
    end
  end
end
