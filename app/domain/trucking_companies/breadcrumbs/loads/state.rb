module TruckingCompanies
  module Breadcrumbs
    module Loads
      class State < TruckingCompanies::Breadcrumbs::Loads::Country
        attr_reader :state

        def initialize(record, state)
          super(record, state.country)
          @state = state
        end

        def items
          super.push({ text: state.name, href: Routes.state_load_trucking_companies_url(record, *state.path) })
        end
      end
    end
  end
end
