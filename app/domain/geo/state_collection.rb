module Geo
  class StateCollection
    include Enumerable

    attr_reader :country, :states

    def initialize(country:, states:)
      @country = country
      @states = states
    end

    def each(&)
      return enum_for(:each) unless block_given?

      states.each(&)
    end

    # rubocop:disable Metrics
    def where(**opts)
      case opts
      in { id: ids }
        Array.wrap(ids).map { |id| indexed_by_id[id] }
      in { abbr: abbrs }
        Array.wrap(abbrs).map { |abbr| indexed_by_abbr[abbr] }
      in { slug: slugs }
        Array.wrap(slugs).map { |slug| indexed_by_slug[slug] }
      in { name: names }
        Array.wrap(names).map { |name| indexed_by_name[name] }
      else
        raise ArgumentError, "Cannot find states by #{opts.inspect}"
      end
    end
    # rubocop:enable Metrics

    def find_by(...)
      where(...).first || Nullable.object('geo/state')
    end

    private

    def indexed_by_id
      @indexed_by_id ||= states.index_by(&:id)
    end

    def indexed_by_name
      @indexed_by_name ||= states.index_by(&:name)
    end

    def indexed_by_abbr
      @indexed_by_abbr ||= states.each_with_object({}) do |state, memo|
        Set.new([state.abbr].concat(state.abbrs)).each { |abbr| memo[abbr] = state }
      end
    end

    def indexed_by_slug
      @indexed_by_slug ||= states.index_by(&:slug)
    end
  end
end
