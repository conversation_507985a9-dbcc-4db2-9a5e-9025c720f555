module CarrierDashboard
  module Navigation
    class ShipperIntent
      include Page

      self.icon = 'engine-regular'
      self.access_feature = :shipper_leads

      Targets = Class.new do
        include Page

        self.icon = 'bullseye-arrow-solid'

        def url
          Routes.carrier_dashboard_analytics_feeds_url(carrier)
        end
      end

      Signals = Class.new do
        include Page

        self.icon = 'signal-stream-regular'

        def url
          Routes.carrier_dashboard_analytics_feed_shipper_intents_url(carrier, '~')
        end
      end

      Integrations = Class.new do
        include Page

        self.icon = 'link-solid'

        def url
          Routes.carrier_dashboard_analytics_integrations_url(carrier)
        end
      end

      register(:targets) { Targets }
      register(:signals) { Signals }
      register(:integrations) { Integrations }

      def url
        Routes.carrier_dashboard_analytics_root_url(carrier)
      end
    end
  end
end
