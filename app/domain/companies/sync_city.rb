module Companies
  class SyncCity
    include Callable

    attr_reader :company

    delegate :zip, :country_code, to: :company

    def initialize(company)
      @company = company
    end

    def call
      return if company.city_id == city.id
      company.update_column(:city_id, city.id)
    end

    private

    def city
      return @city if defined? @city
      @city = if zip.blank? || country_code.blank?
                Nullable.object(:city)
              else
                PostalCode.find_by(code: zip, country_code:).try(:city) || Nullable.object(:city)
              end
    end
  end
end
