module Companies
  module EntityClassRegistry
    extend Dry::Container::Mixin

    namespace :completion_progress do
      register :broker, BrokerageProfiles::CompletionProgress
      register :carrier, CarrierProfiles::CompletionProgress
    end

    namespace :dashboard_navigation do
      register :broker, BrokerageDashboard::Navigation
      register :carrier, CarrierDashboard::Navigation
    end

    namespace :view_model do
      register :broker, Brokerages::BrokerageInfo
      register :carrier, Carriers::CarrierInfo
    end

    def self.lookup(entity:, type:)
      resolve("#{type}.#{entity.entity_type}")
    end
  end
end
