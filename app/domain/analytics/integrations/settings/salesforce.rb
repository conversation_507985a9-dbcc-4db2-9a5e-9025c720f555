module Analytics
  module Integrations
    class Settings
      class Salesforce
        include StoreModel::Model

        attribute :token_type, :string
        attribute :access_token, :string
        attribute :refresh_token, :string
        attribute :instance_url, :string
        attribute :id, :string
        attribute :issued_at, :string
        attribute :signature, :string
        attribute :scope, :string
        attribute :expires_at, :datetime

        def expired?
          expires_at <= Time.zone.now
        end
      end
    end
  end
end
