module Analytics
  module Companies
    class Serialize
      include Callable

      attr_reader :company

      delegate :id, :name, :industry_id, :employee_range, :city_id, to: :company
      delegate :location, :state, :country, to: :city

      def initialize(company)
        @company = company
      end

      def call
        {
          id:, size: employee_range.as_text, name:, industry_id:,
          city_id:, location:, state: state.id, region: state.region.id, country: country.slug
        }.compact_blank
      end

      private

      def city
        @city ||= company.city || Nullable.object(:city)
      end
    end
  end
end
