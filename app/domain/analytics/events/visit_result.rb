module Analytics
  module Events
    class VisitResult
      module SearchSignalLoads
        def loads
          @loads ||= {
            search_freight_ids: :freights,
            search_truck_type_ids: :truck_types,
            search_shipment_type_ids: :shipment_types,
            search_specialized_service_ids: :specialized_services
          }.flat_map { |key, type| Records[type].locate_many(event[key]) }.compact.uniq.presence || [NullLoad.instance]
        end
      end

      SignalAggregation = Data.define(:item, :count)

      SearchSignal = Struct.new(:event) do
        include SearchSignalLoads

        def label
          "#{loads.map(&:name).join(', ')} in #{city.label} to #{destination_city.label}"
        end

        def city
          @city ||= City.find(event.search_city)
        end

        def destination_city
          @destination_city ||= City.find_by(id: event.search_destination_city) || Nullable.object(:city)
        end
      end

      StateSearchSignal = Struct.new(:event) do
        include SearchSignalLoads

        def label
          "#{loads.map(&:name).join(', ')} in #{state.name}"
        end

        def state
          @state ||= ::Geo::State.find(event.search_state)
        end
      end

      SimilarSignal = Data.define(:company, :types)

      EVENT_GROUPER = proc do |company_id, event|
        if Signals::Company.key?(event.type)
          event.target_company.id == company_id ? :direct : :indirect
        else
          :search
        end
      end

      attr_reader :company, :record, :result

      delegate_missing_to :record

      def initialize(company, record, result)
        @company = company
        @record = record
        @result = result
      end

      def signals
        @signals ||=
          grouped_events.each_with_object({}) do |(scope, events), memo|
            memo[scope] = events.group_by(&:type).map do |type, grouped|
              if Signals::Company.key?(type)
                SignalAggregation.new(item: Signals::Company.public_send(scope, type), count: grouped.size)
              else
                SignalAggregation.new(item: Signals::Search[type], count: grouped.size)
              end
            end
          end
      end

      def events
        @events ||= result.inner_hits.events.hits.hits.map(&:_source)
      end

      def search_signals
        @search_signals ||=
          Array.wrap(grouped_events[:search])
            .select { |event| event.search_city.present? }
            .map { |event| SearchSignal.new(event) }
            .uniq(&:label)
      end

      def state_search_signals
        @state_search_signals ||=
          Array.wrap(grouped_events[:search])
            .select { |event| event.search_state.present? && event.search_city.blank? }
            .map { |event| StateSearchSignal.new(event) }
            .uniq(&:label)
      end

      # rubocop:disable Metrics
      def similar_signals
        @similar_signals ||=
          Array.wrap(grouped_events[:indirect])
            .group_by { |event| [event.target_company.id, event.target_company.entity_type] }
            .transform_values { |events| events.map(&:type).uniq }.then do |hash|
            companies = ::Company.where(id: hash.keys.map(&:first)).index_by(&:id)
            hash.map do |(company_id, entity_type), types|
              SimilarSignal.new(
                companies[company_id].as_entity(entity_type), types.map { |type| Signals::Company.indirect(type) }
              )
            end
          end
      end
      # rubocop:enable Metrics

      def grouped_events
        @grouped_events ||= events.group_by(&EVENT_GROUPER.curry[company.id])
      end
    end
  end
end
