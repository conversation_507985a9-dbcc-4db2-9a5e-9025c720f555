module CarrierProfiles
  module VectorSearchAssets
    module Sources
      class Loads
        include Callable

        NAME_RENDERER = proc { |record| "- #{record.name}" }

        attr_reader :carrier_profile

        delegate :carrier, to: :carrier_profile
        delegate :freights, :truck_types, :shipment_types, :specialized_services, to: :carrier

        def initialize(carrier_profile)
          @carrier_profile = carrier_profile
        end

        def call
          [
            "#{carrier.name} Equipment and Services:\n",
            "Fleet Size: #{carrier.power_units} trucks\n",
            'Freights:',
            *freights.map(&NAME_RENDERER),
            '',
            'Truck Types:',
            *truck_types.map(&NAME_RENDERER),
            '',
            'Shipment Types:',
            *shipment_types.map(&NAME_RENDERER),
            '',
            'Specialized Services:',
            *specialized_services.map(&NAME_RENDERER)
          ].join("\n")
        end
      end
    end
  end
end
