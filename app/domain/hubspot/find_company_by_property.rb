module Hubspot
  class FindCompanyByProperty
    include Callable

    attr_reader :property, :value, :token

    def initialize(property:, value:, token:)
      @property = property
      @value = value
      @token = token
    end

    def call
      Hubspot::Api::Crm::Companies.new(token:, raise_errors: true).search(payload)
        .then { |response| response.parse.dig('results', 0).presence }
    end

    private

    def payload
      {
        filterGroups: [
          {
            filters: [
              {
                propertyName: property,
                operator: 'EQ',
                value:
              }
            ]
          }
        ]
      }
    end
  end
end
