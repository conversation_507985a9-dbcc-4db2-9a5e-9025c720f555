<div class="flex flex-col items-center">
  <div class="w-full md:w-[480px] space-y-4">
    <div class="p-6 border border-gray-200 rounded-sm shadow-xl">
      <% if title %>
        <h1 class="text-xl font-semibold text-black mb-3"><%= title %></h1>
      <% end %>

      <%= button_to '/auth/linkedin', form_class: 'flex justify-center mx-auto',
                    class: 'bg-[#0077b5] rounded-sm w-full text-white h-11 mt-3 mb-6 hover:bg-primary-600
                            focus:outline:bg-primary-700 x-btn-signup-with-linkedin',
                    data: { turbo: false } do %>
        <span class="flex flex-row gap-3 justify-center items-center">
          <%= helpers.svg_tag('linkedin-in-brands', class: 'w-4 h-4 fill-white') %>
          <%= t('users.new.linkedin') %>
        </span>
      <% end %>

      <%= button_to '/auth/facebook', form_class: 'flex justify-center mx-auto',
                    class: 'bg-[#4267b2] rounded-sm w-full text-white h-11 mb-6 hover:bg-primary-600
                            focus:outline:bg-primary-700 x-btn-sign-in-with-facebook',
                    data: { turbo: false } do %>
        <span class="flex flex-row gap-3 justify-center items-center">
          <%= helpers.svg_tag('square-facebook', class: 'w-4 h-4 fill-white') %>
          <%= t('users.new.facebook') %>
        </span>
      <% end %>

      <%= button_to '/auth/google', form_class: 'flex justify-center mx-auto',
                    class: 'border border-gray-400 rounded-sm w-full h-11 mb-6
                            focus:outline:bg-primary-700 x-btn-sign-in-with-google',
                    data: { turbo: false } do %>
        <span class="flex flex-row gap-3 justify-center items-center">
          <%= helpers.svg_tag('square-gsi', class: 'w-4 h-4') %>
          <%= t('users.new.google') %>
        </span>
      <% end %>

      <div class="flex justify-center items-center w-full bg-gray-300 h-[1px] mt-6 mb-10">
        <span class="bg-white px-3">or</span>
      </div>

      <%= render 'users/form', model: user, url: users_url(return_to:) %>
    </div>
  </div>

  <div class="text-sm mt-6">
    <span> Already have an account?
      <%= link_to t('users.new.sign_in'), sign_in_path(return_to:),
                  class: 'text-primary font-semibold hover:underline x-link-already-have-account-sign-in' %>
    </span>
  </div>
</div>
